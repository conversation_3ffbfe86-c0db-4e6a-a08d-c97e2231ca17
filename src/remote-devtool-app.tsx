import ReactDOM from 'react-dom/client';
import App from './app';

declare global {
  interface Window {
    __USER_INFO__?: string;
  }
}

// Whitelist logic (moved from WhitelistProvider)
const cacheKey = window.crypto.getRandomValues(new Int16Array(1))[0] || 1;
const WHITE_LIST_URL = `https://simg.zalopay.com.vn/fs/dev-tools/white-list.json?cacheKey=${Math.abs(cacheKey)}`;
const WHITE_LIST_UID = `https://simg.zalopay.com.vn/fs/dev-tools/white-list-uid.json?cacheKey=${Math.abs(cacheKey)}`;

let whiteList: string[] = [];
let whiteListUIDs: string[] = [];
let whitelistDataLoaded = false;

function checkPathWhitelist(): boolean {
  const url = window.location.href;
  return whiteList.some((path: string) => url.includes(path));
}

function checkAppsWhitelist(apps: string[]): boolean {
  return apps.some((app) => whiteList.includes(app));
}

function checkUserWhitelist(): boolean {
  const userID = window.__USER_INFO__
    ? JSON.parse(window.__USER_INFO__)?.zalopay_id
    : undefined;
  return userID && whiteListUIDs.some((uid) => uid === String(userID));
}

function checkAllWhitelists(apps: string[] = []): boolean {
  const pathEnabled = checkPathWhitelist();
  const appsEnabled = apps.length > 0 ? checkAppsWhitelist(apps) : false;
  const userEnabled = checkUserWhitelist();

  // Using OR logic as per user preference
  return pathEnabled || appsEnabled || userEnabled;
}

async function loadWhitelistData(): Promise<void> {
  if (whitelistDataLoaded) return;

  try {
    const [whiteListResponse, whiteListUIDResponse] = await Promise.all([
      fetch(WHITE_LIST_URL),
      fetch(WHITE_LIST_UID),
    ]);

    const [whiteListData, whiteListUIDData] = await Promise.all([
      whiteListResponse.json(),
      whiteListUIDResponse.json(),
    ]);

    if (whiteListData?.white_list?.length) {
      whiteList = whiteListData.white_list;
    }

    if (whiteListUIDData?.white_list_uid?.length) {
      whiteListUIDs = whiteListUIDData.white_list_uid;
    }

    whitelistDataLoaded = true;
  } catch (error) {
    console.error('Failed to fetch whitelist data:', error);
  }
}

function routingListener(evt: CustomEvent) {
  const apps = evt.detail?.appsByNewStatus.MOUNTED || [];
  if (apps && apps.length) {
    const isEnabled = checkAllWhitelists(apps);
    if (!isEnabled && root) {
      // If whitelist check fails, unmount the app
      unmount();
    } else if (isEnabled && !root) {
      // If whitelist check passes and app is not mounted, mount it
      mount();
    }
  }
}

function createElement(id: string) {
  const ele = document.createElement('div');
  ele.id = id;
  document.body.append(ele);
}



export const bootstrap = () => {
  console.log('bootstrap');
  return Promise.resolve();
};

let root: ReactDOM.Root | undefined;
export const mount = async () => {
  return new Promise<void>(async (resolve) => {
    // Load whitelist data first
    await loadWhitelistData();

    // Check if devtool should be enabled
    const isEnabled = checkAllWhitelists();

    if (!isEnabled) {
      console.log('Remote devtool not enabled due to whitelist restrictions');
      resolve();
      return;
    }

    const idRoot = 'remote-devtool';
    let ele = document.getElementById(idRoot);
    if (!ele) {
      createElement(idRoot);
      ele = document.getElementById(idRoot);
    }

    root = ReactDOM.createRoot(ele!);
    root.render(<App />);

    // Set up routing listener for single-spa events
    window.addEventListener('single-spa:routing-event', routingListener as EventListener);

    resolve();
  });
};

export const unmount = () => {
  return new Promise<void>((resolve) => {
    root && root.unmount();
    root = undefined;

    // Clean up event listener
    window.removeEventListener('single-spa:routing-event', routingListener as EventListener);

    resolve();
  });
};
