import ReactDOM from 'react-dom/client';
import App from './app';

declare global {
  interface Window {
    __USER_INFO__?: string;
  }
}

// Whitelist logic (same as in remote-devtool-app.tsx)
const cacheKey = window.crypto.getRandomValues(new Int16Array(1))[0] || 1;
const WHITE_LIST_URL = `https://simg.zalopay.com.vn/fs/dev-tools/white-list.json?cacheKey=${Math.abs(cacheKey)}`;
const WHITE_LIST_UID = `https://simg.zalopay.com.vn/fs/dev-tools/white-list-uid.json?cacheKey=${Math.abs(cacheKey)}`;

let whiteList: string[] = [];
let whiteListUIDs: string[] = [];

function checkPathWhitelist(): boolean {
  const url = window.location.href;
  return whiteList.some((path: string) => url.includes(path));
}

function checkAppsWhitelist(apps: string[]): boolean {
  return apps.some((app) => whiteList.includes(app));
}

function checkUserWhitelist(): boolean {
  const userID = window.__USER_INFO__
    ? JSON.parse(window.__USER_INFO__)?.zalopay_id
    : undefined;
  return userID && whiteListUIDs.some((uid) => uid === String(userID));
}

function checkAllWhitelists(apps: string[] = []): boolean {
  const pathEnabled = checkPathWhitelist();
  const appsEnabled = apps.length > 0 ? checkAppsWhitelist(apps) : false;
  const userEnabled = checkUserWhitelist();

  // Using OR logic as per user preference
  return pathEnabled || appsEnabled || userEnabled;
}

async function loadWhitelistData(): Promise<void> {
  try {
    const [whiteListResponse, whiteListUIDResponse] = await Promise.all([
      fetch(WHITE_LIST_URL),
      fetch(WHITE_LIST_UID),
    ]);

    const [whiteListData, whiteListUIDData] = await Promise.all([
      whiteListResponse.json(),
      whiteListUIDResponse.json(),
    ]);

    if (whiteListData?.white_list?.length) {
      whiteList = whiteListData.white_list;
    }

    if (whiteListUIDData?.white_list_uid?.length) {
      whiteListUIDs = whiteListUIDData.white_list_uid;
    }
  } catch (error) {
    console.error('Failed to fetch whitelist data:', error);
  }
}

function routingListener(evt: CustomEvent) {
  const apps = evt.detail?.appsByNewStatus.MOUNTED || [];
  if (apps && apps.length) {
    const isEnabled = checkAllWhitelists(apps);
    if (!isEnabled) {
      // Hide the app if whitelist check fails
      const rootElement = document.getElementById('root');
      if (rootElement) {
        rootElement.style.display = 'none';
      }
    } else {
      // Show the app if whitelist check passes
      const rootElement = document.getElementById('root');
      if (rootElement) {
        rootElement.style.display = 'block';
      }
    }
  }
}

// Initialize the app with whitelist check
async function initializeApp() {
  // Load whitelist data first
  await loadWhitelistData();

  // Check if devtool should be enabled
  const isEnabled = checkAllWhitelists();

  if (!isEnabled) {
    console.log('Remote devtool not enabled due to whitelist restrictions');
    // Hide the root element
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.style.display = 'none';
    }
    return;
  }

  // Render the app if whitelist check passes
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);

  // Set up routing listener for single-spa events
  window.addEventListener('single-spa:routing-event', routingListener as EventListener);
}

// Start the app
initializeApp();
